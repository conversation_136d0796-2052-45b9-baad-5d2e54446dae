package com.yzedulife.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentImportDTO;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel处理服务接口
 */
public interface ExcelService {

    // 类别权重矩阵：行代表T1-T5问卷，列代表A-F类别
    double[][] CATEGORY_WEIGHTS = {
        {0.0, 0.0, 0.0, 0.0, 0.0, 0.0},  // T1
        {0.6, 0.3, 0.0, 0.0, 0.0, 0.65}, // T2
        {0.4, 0.55, 0.55, 0.55, 0.2, 0.35}, // T3
        {0.0, 0.15, 0.3, 0.3, 0.5, 0.0},  // T4
        {0.0, 0.0, 0.15, 0.15, 0.3, 0.0}  // T5
    };

    // 类别简称映射
    Map<String, String> CATEGORY_SHORT_NAMES = new HashMap<String, String>() {{
        put("A", "AU");
        put("B", "EL");
        put("C", "AE");
        put("D", "OA");
        put("E", "BE");
        put("F", "ME");
    }};

    // 类别全称映射
    Map<String, String> CATEGORY_FULL_NAMES = new HashMap<String, String>() {{
        put("A", "汽车修理与应用 Automotive");
        put("B", "电子电工 Electricity");
        put("C", "CAD制图/After Effect 视频后期制作");
        put("D", "办公自动化 Office Automation");
        put("E", "商务英语 Business English");
        put("F", "机械加工 —— 车床与数控或焊接Mechanics");
    }};

    /**
     * 从Excel文件导入学生数据
     * @param inputStream Excel文件输入流
     * @param fileName 文件名
     * @return 导入结果列表
     */
    List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException;

    /**
     * 导出班级答题数据到Excel
     * @param classId 班级ID
     * @param questionnaireIds 问卷ID列表（5个问卷）
     * @return Excel文件路径
     */
    String exportClassAnswerData(Long classId, List<Long> questionnaireIds) throws BusinessException;
}
