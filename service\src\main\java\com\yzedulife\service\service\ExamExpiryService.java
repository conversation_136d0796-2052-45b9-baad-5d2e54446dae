package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.ExamExpiryDTO;

/**
 * 考试过期时间服务接口
 */
public interface ExamExpiryService {

    /**
     * 创建或更新考试过期时间
     * @param questionnaireId 问卷ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @param durationSeconds 考试时长（秒）
     * @return 考试过期时间DTO
     * @throws BusinessException 业务异常
     */
    ExamExpiryDTO createOrUpdate(Long questionnaireId, Long userId, String userType, Integer durationSeconds) throws BusinessException;

    /**
     * 查询考试过期时间
     * @param questionnaireId 问卷ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 考试过期时间DTO
     * @throws BusinessException 业务异常
     */
    ExamExpiryDTO query(Long questionnaireId, Long userId, String userType) throws BusinessException;

    /**
     * 查询考试过期时间（不自动清理过期记录）
     * @param questionnaireId 问卷ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 考试过期时间DTO（包括已过期的记录）
     * @throws BusinessException 业务异常
     */
    ExamExpiryDTO queryWithoutCleanup(Long questionnaireId, Long userId, String userType) throws BusinessException;

    /**
     * 删除考试过期时间
     * @param questionnaireId 问卷ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 是否删除成功
     * @throws BusinessException 业务异常
     */
    Boolean delete(Long questionnaireId, Long userId, String userType) throws BusinessException;

    /**
     * 删除指定问卷的所有考试过期时间
     * @param questionnaireId 问卷ID
     * @return 删除的记录数
     * @throws BusinessException 业务异常
     */
    Integer deleteByQuestionnaireId(Long questionnaireId) throws BusinessException;
}
