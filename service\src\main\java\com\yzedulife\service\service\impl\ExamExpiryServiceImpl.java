package com.yzedulife.service.service.impl;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.ExamExpiryDTO;
import com.yzedulife.service.service.ExamExpiryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 考试过期时间服务实现类
 * 使用内存存储考试过期时间
 */
@Slf4j
@Service
public class ExamExpiryServiceImpl implements ExamExpiryService {

    /**
     * 考试过期时间存储容器
     * key格式: questionnaireId_userId_userType
     */
    private final ConcurrentHashMap<String, ExamExpiryDTO> expiryStorage = new ConcurrentHashMap<>();

    @Override
    public ExamExpiryDTO createOrUpdate(Long questionnaireId, Long userId, String userType, Integer durationSeconds) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (userType == null || userType.trim().isEmpty()) {
            throw new BusinessException("用户类型不能为空");
        }
        if (durationSeconds == null || durationSeconds <= 0) {
            throw new BusinessException("考试时长必须大于0");
        }

        String key = generateKey(questionnaireId, userId, userType);
        
        // 检查是否已存在
        ExamExpiryDTO existingExpiry = expiryStorage.get(key);
        if (existingExpiry != null) {
            // 如果已存在且未过期，直接返回
            if (existingExpiry.getExpiry().isAfter(LocalDateTime.now())) {
                log.info("考试过期时间已存在，问卷ID：{}，用户ID：{}，用户类型：{}，过期时间：{}", 
                    questionnaireId, userId, userType, existingExpiry.getExpiry());
                return existingExpiry;
            }
        }

        // 创建新的过期时间
        LocalDateTime expiry = LocalDateTime.now().plusSeconds(durationSeconds);
        ExamExpiryDTO examExpiryDTO = new ExamExpiryDTO(questionnaireId, userId, userType, expiry);
        expiryStorage.put(key, examExpiryDTO);

        log.info("创建考试过期时间成功，问卷ID：{}，用户ID：{}，用户类型：{}，过期时间：{}", 
            questionnaireId, userId, userType, expiry);
        return examExpiryDTO;
    }

    @Override
    public ExamExpiryDTO query(Long questionnaireId, Long userId, String userType) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (userType == null || userType.trim().isEmpty()) {
            throw new BusinessException("用户类型不能为空");
        }

        String key = generateKey(questionnaireId, userId, userType);
        ExamExpiryDTO examExpiryDTO = expiryStorage.get(key);
        
        if (examExpiryDTO == null) {
            return null;
        }

        // 检查是否过期
        if (examExpiryDTO.getExpiry().isBefore(LocalDateTime.now())) {
            expiryStorage.remove(key);
            return null;
        }

        return examExpiryDTO;
    }

    @Override
    public ExamExpiryDTO queryWithoutCleanup(Long questionnaireId, Long userId, String userType) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (userType == null || userType.trim().isEmpty()) {
            throw new BusinessException("用户类型不能为空");
        }

        String key = generateKey(questionnaireId, userId, userType);
        ExamExpiryDTO examExpiryDTO = expiryStorage.get(key);

        // 直接返回记录，不检查是否过期，不自动清理
        return examExpiryDTO;
    }

    @Override
    public Boolean delete(Long questionnaireId, Long userId, String userType) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (userType == null || userType.trim().isEmpty()) {
            throw new BusinessException("用户类型不能为空");
        }

        String key = generateKey(questionnaireId, userId, userType);
        ExamExpiryDTO removed = expiryStorage.remove(key);
        return removed != null;
    }

    @Override
    public Integer deleteByQuestionnaireId(Long questionnaireId) throws BusinessException {
        if (questionnaireId == null) {
            throw new BusinessException("问卷ID不能为空");
        }

        int deletedCount = 0;
        String prefix = questionnaireId + "_";

        // 遍历所有key，删除匹配的记录
        for (String key : expiryStorage.keySet()) {
            if (key.startsWith(prefix)) {
                ExamExpiryDTO removed = expiryStorage.remove(key);
                if (removed != null) {
                    deletedCount++;
                }
            }
        }

        log.info("删除问卷{}的所有考试过期时间，共删除{}条记录", questionnaireId, deletedCount);
        return deletedCount;
    }

    /**
     * 生成存储key
     */
    private String generateKey(Long questionnaireId, Long userId, String userType) {
        return questionnaireId + "_" + userId + "_" + userType.toUpperCase();
    }
}
