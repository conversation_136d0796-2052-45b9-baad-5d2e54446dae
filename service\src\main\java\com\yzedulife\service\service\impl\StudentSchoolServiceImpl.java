package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.service.entity.StudentSchool;
import com.yzedulife.service.mapper.StudentSchoolMapper;
import com.yzedulife.service.service.StudentSchoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学生学校服务实现类
 */
@Service
public class StudentSchoolServiceImpl implements StudentSchoolService {

    @Autowired
    private StudentSchoolMapper studentSchoolMapper;

    @Override
    public StudentSchoolDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学校ID不能为空");
        }
        StudentSchool studentSchool = studentSchoolMapper.selectById(id);
        if (studentSchool == null) {
            throw new BusinessException("学校不存在");
        }
        return studentSchool.toDTO();
    }

    @Override
    public StudentSchoolDTO getBySchoolName(String schoolName) throws BusinessException {
        if (!StringUtils.hasText(schoolName)) {
            throw new BusinessException("学校名称不能为空");
        }
        StudentSchool studentSchool = studentSchoolMapper.selectOne(new LambdaQueryWrapper<StudentSchool>()
                .eq(StudentSchool::getSchoolName, schoolName));
        if (studentSchool == null) {
            throw new BusinessException("学校不存在");
        }
        return studentSchool.toDTO();
    }

    @Override
    public StudentSchoolDTO create(StudentSchoolDTO studentSchoolDTO) throws BusinessException {
        if (studentSchoolDTO == null) {
            throw new BusinessException("学校信息不能为空");
        }
        if (!StringUtils.hasText(studentSchoolDTO.getSchoolName())) {
            throw new BusinessException("学校名称不能为空");
        }
        
        // 检查学校名称是否已存在
        if (isSchoolNameExist(studentSchoolDTO.getSchoolName())) {
            throw new BusinessException("学校名称已存在");
        }
        
        StudentSchool studentSchool = studentSchoolDTO.toEntity();
        studentSchool.setId(null); // 确保是新增
        int result = studentSchoolMapper.insert(studentSchool);
        if (result <= 0) {
            throw new BusinessException("创建学校失败");
        }
        return studentSchool.toDTO();
    }

    @Override
    public StudentSchoolDTO update(StudentSchoolDTO studentSchoolDTO) throws BusinessException {
        if (studentSchoolDTO == null || studentSchoolDTO.getId() == null) {
            throw new BusinessException("学校ID不能为空");
        }
        
        // 检查学校是否存在
        StudentSchool existingSchool = studentSchoolMapper.selectById(studentSchoolDTO.getId());
        if (existingSchool == null) {
            throw new BusinessException("学校不存在");
        }
        
        // 如果修改了学校名称，检查新名称是否已存在
        if (StringUtils.hasText(studentSchoolDTO.getSchoolName()) && 
            !studentSchoolDTO.getSchoolName().equals(existingSchool.getSchoolName())) {
            if (isSchoolNameExist(studentSchoolDTO.getSchoolName())) {
                throw new BusinessException("学校名称已存在");
            }
        }
        
        // 更新字段
        if (StringUtils.hasText(studentSchoolDTO.getSchoolName())) {
            existingSchool.setSchoolName(studentSchoolDTO.getSchoolName());
        }
        
        int result = studentSchoolMapper.updateById(existingSchool);
        if (result <= 0) {
            throw new BusinessException("更新学校失败");
        }
        return existingSchool.toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学校ID不能为空");
        }
        
        StudentSchool studentSchool = studentSchoolMapper.selectById(id);
        if (studentSchool == null) {
            throw new BusinessException("学校不存在");
        }
        
        // 删除学校
        int result = studentSchoolMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<StudentSchoolDTO> getAll() throws BusinessException {
        List<StudentSchool> studentSchools = studentSchoolMapper.selectList(new LambdaQueryWrapper<StudentSchool>()
                .orderByDesc(StudentSchool::getId));
        return studentSchools.stream().map(StudentSchool::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean isExist(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("学校ID不能为空");
        }
        return studentSchoolMapper.exists(new LambdaQueryWrapper<StudentSchool>()
                .eq(StudentSchool::getId, id));
    }

    @Override
    public Boolean isSchoolNameExist(String schoolName) throws BusinessException {
        if (!StringUtils.hasText(schoolName)) {
            throw new BusinessException("学校名称不能为空");
        }
        return studentSchoolMapper.exists(new LambdaQueryWrapper<StudentSchool>()
                .eq(StudentSchool::getSchoolName, schoolName));
    }
}
