package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.QuestionnairePageConvert;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷页面实体类
 */
@Data
@TableName("questionnaire_pages")
public class QuestionnairePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页面ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属问卷ID
     */
    @TableField("questionnaire_id")
    private Long questionnaireId;

    /**
     * 页码
     */
    @TableField("page_number")
    private Integer pageNumber;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public QuestionnairePageDTO toDTO() {
        return QuestionnairePageConvert.INSTANCE.entity2dto(this);
    }
}
