package com.yzedulife.convert;

import com.yzedulife.response.StudentImportResponse;
import com.yzedulife.service.dto.StudentImportDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 学生导入应用层转换器
 */
@Mapper
public interface StudentImportAppConvert {

    StudentImportAppConvert INSTANCE = Mappers.getMapper(StudentImportAppConvert.class);

    /**
     * 将导入结果转换为响应对象
     */
    default StudentImportResponse buildImportResponse(List<StudentImportDTO> successList,
                                                     List<StudentImportDTO> failureList,
                                                     List<StudentImportDTO> duplicateList,
                                                     int totalCount) {
        StudentImportResponse response = new StudentImportResponse();
        response.setTotalCount(totalCount);
        response.setSuccessCount(successList.size());
        response.setFailureCount(failureList.size());
        response.setDuplicateCount(duplicateList.size());

        // 转换失败详情
        List<StudentImportResponse.ImportFailureDetail> failureDetails =
            failureList.stream()
                .map(this::dto2FailureDetail)
                .collect(java.util.stream.Collectors.toList());

        // 转换重复详情
        List<StudentImportResponse.ImportDuplicateDetail> duplicateDetails =
            duplicateList.stream()
                .map(this::dto2DuplicateDetail)
                .collect(java.util.stream.Collectors.toList());

        response.setFailureDetails(failureDetails);
        response.setDuplicateDetails(duplicateDetails);
        return response;
    }

    /**
     * 将导入DTO转换为失败详情
     */
    default StudentImportResponse.ImportFailureDetail dto2FailureDetail(StudentImportDTO dto) {
        StudentImportResponse.ImportFailureDetail detail = new StudentImportResponse.ImportFailureDetail();
        detail.setRowNumber(dto.getRowNumber());
        detail.setSchoolName(dto.getSchoolName());
        detail.setClassName(dto.getClassName());
        detail.setStudentNumber(dto.getStudentNumber());
        detail.setName(dto.getName());
        // 使用具体的错误信息
        detail.setReason(dto.getErrorMessage() != null ? dto.getErrorMessage() : "导入失败");
        return detail;
    }

    /**
     * 将导入DTO转换为重复详情
     */
    default StudentImportResponse.ImportDuplicateDetail dto2DuplicateDetail(StudentImportDTO dto) {
        StudentImportResponse.ImportDuplicateDetail detail = new StudentImportResponse.ImportDuplicateDetail();
        detail.setRowNumber(dto.getRowNumber());
        detail.setSchoolName(dto.getSchoolName());
        detail.setClassName(dto.getClassName());
        detail.setStudentNumber(dto.getStudentNumber());
        detail.setName(dto.getName());
        detail.setReason("学号已存在");
        return detail;
    }
}
