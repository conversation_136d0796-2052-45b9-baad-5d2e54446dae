package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.StudentSchoolConvert;
import com.yzedulife.service.dto.StudentSchoolDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生学校实体类
 */
@Data
@TableName("student_schools")
public class StudentSchool implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学校名称
     */
    @TableField("school_name")
    private String schoolName;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public StudentSchoolDTO toDTO() {
        return StudentSchoolConvert.INSTANCE.entity2dto(this);
    }
}
