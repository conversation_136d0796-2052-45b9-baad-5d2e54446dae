package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerSheetDTO;

import java.util.List;

/**
 * 答卷服务接口
 */
public interface AnswerSheetService {

    /**
     * 根据ID获取答卷
     */
    AnswerSheetDTO getById(Long id) throws BusinessException;

    /**
     * 创建答卷
     */
    AnswerSheetDTO create(AnswerSheetDTO answerSheetDTO) throws BusinessException;

    /**
     * 更新答卷
     */
    AnswerSheetDTO update(AnswerSheetDTO answerSheetDTO) throws BusinessException;

    /**
     * 删除答卷
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 根据问卷ID获取所有答卷
     */
    List<AnswerSheetDTO> getByQuestionnaireId(Long questionnaireId) throws BusinessException;

    /**
     * 根据学生用户ID获取答卷
     */
    List<AnswerSheetDTO> getByStudentUserId(Long studentUserId) throws BusinessException;

    /**
     * 根据社会人士用户ID获取答卷
     */
    List<AnswerSheetDTO> getByOtherUserId(Long otherUserId) throws BusinessException;

    /**
     * 提交答卷
     */
    Boolean submitAnswerSheet(Long answerSheetId) throws BusinessException;

    /**
     * 统计问卷答卷数量
     */
    Integer getCountByQuestionnaireId(Long questionnaireId) throws BusinessException;

    /**
     * 检查用户是否已答题
     */
    Boolean hasUserAnswered(Long questionnaireId, String submitterType, Long userId) throws BusinessException;

    /**
     * 获取所有答卷
     */
    List<AnswerSheetDTO> getAll() throws BusinessException;

    /**
     * 根据问卷ID和提交者类型获取答卷
     */
    List<AnswerSheetDTO> getByQuestionnaireIdAndSubmitterType(Long questionnaireId, String submitterType) throws BusinessException;
}
