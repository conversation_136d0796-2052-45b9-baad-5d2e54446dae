package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.OtherUserDTO;

import java.util.List;

/**
 * 社会人士用户服务接口
 */
public interface OtherUserService {

    /**
     * 根据ID获取社会人士用户
     */
    OtherUserDTO getById(Long id) throws BusinessException;

    /**
     * 根据手机号获取社会人士用户
     */
    OtherUserDTO getByPhone(String phone) throws BusinessException;

    /**
     * 创建社会人士用户
     */
    OtherUserDTO create(OtherUserDTO otherUserDTO) throws BusinessException;

    /**
     * 更新社会人士用户
     */
    OtherUserDTO update(OtherUserDTO otherUserDTO) throws BusinessException;

    /**
     * 删除社会人士用户
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有社会人士用户
     */
    List<OtherUserDTO> getAll() throws BusinessException;

    /**
     * 检查用户是否存在
     */
    Boolean isExist(Long id) throws BusinessException;

    /**
     * 检查手机号是否存在
     */
    Boolean isPhoneExist(String phone) throws BusinessException;

    /**
     * 根据手机号和姓名获取社会人士用户
     */
    OtherUserDTO getByPhoneAndName(String phone, String name) throws BusinessException;
}
