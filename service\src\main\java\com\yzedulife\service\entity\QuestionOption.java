package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.QuestionOptionConvert;
import com.yzedulife.service.dto.QuestionOptionDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 题目选项实体类
 */
@Data
@TableName("question_options")
public class QuestionOption implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属题目ID
     */
    @TableField("question_id")
    private Long questionId;

    /**
     * 选项类型 (TEXT/IMAGE)
     */
    @TableField("option_type")
    private String optionType;

    /**
     * 内容 (文本/URL)
     */
    @TableField("content")
    private String content;

    /**
     * 选项代号
     */
    @TableField("option_code")
    private String optionCode;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public QuestionOptionDTO toDTO() {
        return QuestionOptionConvert.INSTANCE.entity2dto(this);
    }
}
