package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.convert.QuestionnairePageAppConvert;
import com.yzedulife.response.QuestionnairePageResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.dto.QuestionnairePageDTO;
import com.yzedulife.service.service.QuestionnairePageService;
import com.yzedulife.vo.QuestionnairePageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/page")
@Tag(name = "问卷页模块")
@Transactional
public class PageController {

    @Autowired
    private QuestionnairePageService questionnairePageService;

    @Token("admin")
    @Operation(summary = "创建问卷页")
    @PostMapping("/create")
    public Response create(@Valid @RequestBody QuestionnairePageVO questionnairePageVO) {
        try {
            QuestionnairePageDTO dto = QuestionnairePageAppConvert.INSTANCE.vo2dto(questionnairePageVO);
            QuestionnairePageDTO result = questionnairePageService.create(dto);
            QuestionnairePageResponse response = QuestionnairePageAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建问卷页失败", e);
            return Response.error().msg("创建问卷页失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除问卷页")
    @PostMapping("/delete")
    public Response delete(@RequestParam Long id) {
        try {
            Boolean result = questionnairePageService.deleteById(id);
            if (result) {
                return Response.success().msg("删除成功");
            } else {
                return Response.error().msg("删除失败");
            }
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除问卷页失败", e);
            return Response.error().msg("删除问卷页失败");
        }
    }
}
