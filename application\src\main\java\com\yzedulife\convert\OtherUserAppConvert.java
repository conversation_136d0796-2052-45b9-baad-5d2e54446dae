package com.yzedulife.convert;

import com.yzedulife.response.OtherUserResponse;
import com.yzedulife.service.dto.OtherUserDTO;
import com.yzedulife.vo.OtherUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 社会人士用户应用层转换器
 */
@Mapper
public interface OtherUserAppConvert {
    OtherUserAppConvert INSTANCE = Mappers.getMapper(OtherUserAppConvert.class);

    /**
     * OtherUserVO转OtherUserDTO
     */
    OtherUserDTO vo2dto(OtherUserVO vo);

    /**
     * OtherUserDTO转OtherUserResponse
     */
    OtherUserResponse dto2response(OtherUserDTO dto);

    /**
     * OtherUserDTO列表转OtherUserResponse列表
     */
    List<OtherUserResponse> dto2responseList(List<OtherUserDTO> dtoList);
}
