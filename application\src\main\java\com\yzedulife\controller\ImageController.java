package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.util.MD5Util;
import com.yzedulife.response.Response;
import com.yzedulife.response.UploadResponse;
import com.yzedulife.service.OssService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/img")
@Tag(name = "图片模块")
@Transactional
public class ImageController {

    @Autowired
    private OssService ossService;

    // 允许的图片格式
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp");

    // 最大文件大小：2MB
    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;

    @Token("admin")
    @Operation(summary = "图片上传")
    @PostMapping("/upload")
    public Response upload(@RequestParam("file") MultipartFile file) {
        try {
            // 检查文件是否为空
            if (file.isEmpty()) {
                return Response.error().msg("上传文件不能为空");
            }

            // 检查文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                return Response.error().msg("文件大小不能超过2MB");
            }

            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                return Response.error().msg("文件名不能为空");
            }

            String extension = getFileExtension(originalFilename).toLowerCase();
            if (!ALLOWED_EXTENSIONS.contains(extension)) {
                return Response.error().msg("不支持的文件格式，仅支持：" + String.join(", ", ALLOWED_EXTENSIONS));
            }

            // 生成文件的MD5哈希值作为文件名，避免重复
            String hash = MD5Util.getMd5(file);
            if (hash == null) {
                return Response.error().msg("文件处理失败");
            }

            String filename = hash + "." + extension;

            // 使用OSS服务上传文件，返回直链
            String ossUrl = ossService.upload(file, filename);
            log.info("文件上传成功，文件名：{}，OSS直链：{}", filename, ossUrl);

            // 返回OSS直链
            return Response.success().data(new UploadResponse(ossUrl));

        } catch (Exception e) {
            log.error("文件上传异常", e);
            return Response.error().msg("文件上传失败");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
}
