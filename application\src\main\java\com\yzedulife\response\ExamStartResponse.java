package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 考试开始响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "考试开始响应")
public class ExamStartResponse {
    
    @Schema(description = "当前时间")
    private LocalDateTime currentTime;
    
    @Schema(description = "考试过期时间")
    private LocalDateTime expiry;
}
