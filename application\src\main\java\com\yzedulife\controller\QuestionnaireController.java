package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.convert.QuestionAppConvert;
import com.yzedulife.convert.QuestionnaireAppConvert;
import com.yzedulife.convert.QuestionnairePageAppConvert;
import com.yzedulife.response.*;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import com.yzedulife.util.SecurityUtil;
import com.yzedulife.vo.QuestionnaireVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/questionnaire")
@Tag(name = "问卷模块")
@Transactional
public class QuestionnaireController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionnairePageService questionnairePageService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Autowired
    private ExamExpiryService examExpiryService;

    @Autowired
    private AnswerSheetService answerSheetService;

    @Token("admin")
    @Operation(summary = "创建问卷")
    @PostMapping("/create")
    public Response create(@Valid @RequestBody QuestionnaireVO questionnaireVO) {
        try {
            // 获取当前管理员ID
            String token = SecurityUtil.getToken();
            String adminId = JwtUtil.getId(token);
            
            QuestionnaireDTO dto = QuestionnaireAppConvert.INSTANCE.vo2dto(questionnaireVO);
            dto.setCreatorId(Long.parseLong(adminId));

            QuestionnaireDTO result = questionnaireService.create(dto);
            QuestionnaireResponse response = QuestionnaireAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建问卷失败", e);
            return Response.error().msg("创建问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "修改问卷")
    @PostMapping("/update")
    public Response update(@Valid @RequestBody QuestionnaireVO questionnaireVO) {
        try {
            if (questionnaireVO.getId() == null) {
                return Response.error().msg("问卷ID不能为空");
            }

            // 获取原问卷信息，用于比较状态变化
            QuestionnaireDTO originalQuestionnaire = questionnaireService.getById(questionnaireVO.getId());

            QuestionnaireDTO dto = QuestionnaireAppConvert.INSTANCE.vo2dto(questionnaireVO);
            QuestionnaireDTO result = questionnaireService.update(dto);

            // 检查状态是否发生变化，如果从发布状态(1)改为草稿(0)或结束(2)，则清空所有expiry记录
            if (originalQuestionnaire.getStatus() != null && originalQuestionnaire.getStatus() == 1 &&
                questionnaireVO.getStatus() != null && (questionnaireVO.getStatus() == 0 || questionnaireVO.getStatus() == 2)) {
                try {
                    Integer deletedCount = examExpiryService.deleteByQuestionnaireId(questionnaireVO.getId());
                    log.info("问卷状态从发布改为{}，清空了{}条考试过期时间记录", questionnaireVO.getStatus(), deletedCount);
                } catch (Exception e) {
                    log.warn("清空问卷{}的考试过期时间记录失败", questionnaireVO.getId(), e);
                }
            }

            QuestionnaireResponse response = QuestionnaireAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("修改问卷失败", e);
            return Response.error().msg("修改问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除问卷")
    @PostMapping("/delete")
    public Response delete(@RequestParam Long id) {
        try {
            Boolean result = questionnaireService.deleteById(id);
            if (result) {
                return Response.success().msg("删除成功");
            } else {
                return Response.error().msg("删除失败");
            }
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除问卷失败", e);
            return Response.error().msg("删除问卷失败");
        }
    }

    @Token("admin")
    @Operation(summary = "列出所有问卷")
    @GetMapping("/list")
    public Response list() {
        try {
            List<QuestionnaireDTO> dtoList = questionnaireService.getAll();
            List<QuestionnaireResponse> responseList = QuestionnaireAppConvert.INSTANCE.dto2responseList(dtoList);
            
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取问卷列表失败", e);
            return Response.error().msg("获取问卷列表失败");
        }
    }

    @Token("student other")
    @Operation(summary = "开始考试", description = "返回当前时间和考试过期时间")
    @PostMapping("/startExam")
    public Response startExam(@RequestParam Long id) {
        try {
            // 获取问卷基本信息
            QuestionnaireDTO questionnaireDTO = questionnaireService.getById(id);

            // 获取当前用户信息
            String token = SecurityUtil.getToken();
            String userType = JwtUtil.getType(token);
            String userId = JwtUtil.getId(token);

            // 检查问卷状态，只有发布状态(status=1)的问卷才能被student和other用户访问
            if (questionnaireDTO.getStatus() == null || questionnaireDTO.getStatus() != 1) {
                return Response.error().msg("考试未发布");
            }

            // 检查用户是否已经提交过该问卷的答案
            Boolean hasAnswered = answerSheetService.hasUserAnswered(id, userType.toUpperCase(), Long.parseLong(userId));
            if (hasAnswered) {
                return Response.error().msg("您已经提交过该问卷的答案，不能重复提交");
            }

            LocalDateTime expiry = null;

            // 查询是否已存在过期时间记录（包括已过期的）
            ExamExpiryDTO existingExpiry = examExpiryService.queryWithoutCleanup(id, Long.parseLong(userId), userType.toUpperCase());

            if (existingExpiry != null) {
                // 检查是否已过期
                if (existingExpiry.getExpiry().isBefore(LocalDateTime.now())) {
                    return Response.error().msg("考试已过期");
                }
                // 已存在且未过期，使用现有的过期时间
                expiry = existingExpiry.getExpiry();
            } else {
                // 不存在记录，生成新的过期时间
                if (questionnaireDTO.getDuration() != null && questionnaireDTO.getDuration() > 0) {
                    ExamExpiryDTO newExpiry = examExpiryService.createOrUpdate(
                        id, Long.parseLong(userId), userType.toUpperCase(), questionnaireDTO.getDuration());
                    expiry = newExpiry.getExpiry();
                }
            }

            // 创建响应对象，包含当前时间和过期时间
            ExamStartResponse examStartResponse = new ExamStartResponse();
            examStartResponse.setCurrentTime(LocalDateTime.now());
            examStartResponse.setExpiry(expiry);

            return Response.success().data(examStartResponse).msg("考试开始成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("开始考试失败", e);
            return Response.error().msg("开始考试失败");
        }
    }

    @Token("admin student other")
    @Operation(summary = "问卷详情")
    @GetMapping("/detail")
    public Response detail(@RequestParam Long id) {
        try {
            // 获取问卷基本信息
            QuestionnaireDTO questionnaireDTO = questionnaireService.getById(id);
            QuestionnaireResponse response = QuestionnaireAppConvert.INSTANCE.dto2response(questionnaireDTO);

            // 获取问卷的所有页面
            List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(id);
            List<QuestionnairePageResponse> pageResponses = new ArrayList<>();

            for (QuestionnairePageDTO page : pages) {
                QuestionnairePageResponse pageResponse = QuestionnairePageAppConvert.INSTANCE.dto2response(page);

                // 获取页面的所有题目
                List<QuestionDTO> questions = questionService.getByPageId(page.getId());
                List<QuestionResponse> questionResponses = new ArrayList<>();

                for (QuestionDTO question : questions) {
                    QuestionResponse questionResponse = QuestionAppConvert.INSTANCE.dto2response(question);

                    // 获取题目的所有选项
                    List<QuestionOptionDTO> options = questionOptionService.getByQuestionId(question.getId());
                    List<QuestionOptionResponse> optionResponses = QuestionAppConvert.INSTANCE.optionDto2responseList(options);
                    questionResponse.setOptions(optionResponses);

                    questionResponses.add(questionResponse);
                }

                pageResponse.setQuestions(questionResponses);
                pageResponses.add(pageResponse);
            }

            response.setPages(pageResponses);
            return Response.success().data(response);

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取问卷详情失败", e);
            return Response.error().msg("获取问卷详情失败");
        }
    }
}
