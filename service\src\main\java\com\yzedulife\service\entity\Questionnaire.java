package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.QuestionnaireConvert;
import com.yzedulife.service.dto.QuestionnaireDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷实体类
 */
@Data
@TableName("questionnaires")
public class Questionnaire implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 描述（图片路径）
     */
    @TableField("description")
    private String description;

    /**
     * 目标受众
     */
    @TableField("target_audience")
    private String targetAudience;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 考试时间（秒）
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 创建者ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public QuestionnaireDTO toDTO() {
        return QuestionnaireConvert.INSTANCE.entity2dto(this);
    }
}
