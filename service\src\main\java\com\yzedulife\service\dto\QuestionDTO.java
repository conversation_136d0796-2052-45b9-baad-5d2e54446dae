package com.yzedulife.service.dto;

import com.yzedulife.service.convert.QuestionConvert;
import com.yzedulife.service.entity.Question;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 题目DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QuestionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private Long id;

    /**
     * 所属页面ID
     */
    private Long pageId;

    /**
     * 题干类型
     */
    private String contentType;

    /**
     * 题干内容
     */
    private String content;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 正确答案选项代号 (nullable)
     */
    private String correctAnswerCode;

    /**
     * 题目分类 (nullable)
     */
    private String category;

    public Question toEntity() {
        return QuestionConvert.INSTANCE.dto2entity(this);
    }
}
