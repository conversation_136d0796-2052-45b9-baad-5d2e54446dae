package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentUserDTO;

import java.util.List;

/**
 * 学生用户服务接口
 */
public interface StudentUserService {

    /**
     * 根据ID获取学生用户
     */
    StudentUserDTO getById(Long id) throws BusinessException;

    /**
     * 根据学号获取学生用户
     */
    StudentUserDTO getByStudentNumber(String studentNumber) throws BusinessException;

    /**
     * 创建学生用户
     */
    StudentUserDTO create(StudentUserDTO studentUserDTO) throws BusinessException;

    /**
     * 更新学生用户
     */
    StudentUserDTO update(StudentUserDTO studentUserDTO) throws BusinessException;

    /**
     * 删除学生用户
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有学生用户
     */
    List<StudentUserDTO> getAll() throws BusinessException;

    /**
     * 检查学号是否存在
     */
    Boolean isExist(Long id) throws BusinessException;

    /**
     * 检查学生是否存在
     */
    Boolean isStudentNumberExist(String studentNumber) throws BusinessException;

    /**
     * 检查学号在指定班级内是否存在
     */
    Boolean isStudentNumberExistInClass(String studentNumber, Long classId) throws BusinessException;

    /**
     * 根据班级ID和学号获取学生
     */
    StudentUserDTO getByClassIdAndStudentNumber(Long classId, String studentNumber) throws BusinessException;

    /**
     * 批量导入学生用户
     */
    Boolean batchImport(List<StudentUserDTO> studentUsers) throws BusinessException;

    /**
     * 根据班级ID获取学生列表
     */
    List<StudentUserDTO> getByClassId(Long classId) throws BusinessException;

    /**
     * 根据学校ID获取学生列表
     */
    List<StudentUserDTO> getBySchoolId(Long schoolId) throws BusinessException;
}
