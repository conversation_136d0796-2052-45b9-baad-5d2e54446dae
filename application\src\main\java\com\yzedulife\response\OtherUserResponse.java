package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 社会人士用户响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "社会人士用户响应")
public class OtherUserResponse {

    @Schema(description = "社会人士ID")
    private Long id;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "姓名")
    private String name;
}
