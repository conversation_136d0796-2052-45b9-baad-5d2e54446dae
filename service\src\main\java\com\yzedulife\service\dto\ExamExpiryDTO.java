package com.yzedulife.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 考试过期时间DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExamExpiryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 过期时间
     */
    private LocalDateTime expiry;
}
