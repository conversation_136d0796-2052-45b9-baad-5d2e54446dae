package com.yzedulife.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学生导入DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行号（用于错误定位）
     */
    private Integer rowNumber;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 学号
     */
    private String studentNumber;

    /**
     * 姓名
     */
    private String name;

    /**
     * 错误信息（用于记录导入失败的原因）
     */
    private String errorMessage;

    /**
     * 导入状态：SUCCESS-成功，FAILED-失败，DUPLICATE-重复
     */
    private ImportStatus status;

    /**
     * 导入状态枚举
     */
    public enum ImportStatus {
        SUCCESS("成功"),
        FAILED("失败"),
        DUPLICATE("重复");

        private final String description;

        ImportStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
