package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.MD5Util;
import com.yzedulife.service.dto.AdminDTO;
import com.yzedulife.service.entity.Admin;
import com.yzedulife.service.mapper.AdminMapper;
import com.yzedulife.service.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 管理员服务实现类
 */
@Service
public class AdminServiceImpl implements AdminService {

    @Autowired
    private AdminMapper adminMapper;

    @Override
    public AdminDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("管理员ID不能为空");
        }
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException("管理员不存在");
        }
        return admin.toDTO();
    }

    @Override
    public AdminDTO getByUsername(String username) throws BusinessException {
        if (!StringUtils.hasText(username)) {
            throw new BusinessException("用户名不能为空");
        }
        Admin admin = adminMapper.selectOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username));
        if (admin == null) {
            throw new BusinessException("管理员不存在");
        }
        return admin.toDTO();
    }

    @Override
    public AdminDTO create(AdminDTO adminDTO) throws BusinessException {
        if (adminDTO == null) {
            throw new BusinessException("管理员信息不能为空");
        }
        if (!StringUtils.hasText(adminDTO.getUsername())) {
            throw new BusinessException("用户名不能为空");
        }
        if (!StringUtils.hasText(adminDTO.getPassword())) {
            throw new BusinessException("密码不能为空");
        }

        // 检查用户名是否已存在
        if (isUsernameExist(adminDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        Admin admin = adminDTO.toEntity();
        admin.setId(null); // 确保是新增

        // 对密码进行MD5加密
        String encryptedPassword = MD5Util.getMd5(admin.getPassword());
        if (encryptedPassword == null) {
            throw new BusinessException("密码加密失败");
        }
        admin.setPassword(encryptedPassword);

        int result = adminMapper.insert(admin);
        if (result <= 0) {
            throw new BusinessException("创建管理员失败");
        }
        return admin.toDTO();
    }

    @Override
    public AdminDTO update(AdminDTO adminDTO) throws BusinessException {
        if (adminDTO == null || adminDTO.getId() == null) {
            throw new BusinessException("管理员ID不能为空");
        }

        // 检查管理员是否存在
        Admin existingAdmin = adminMapper.selectById(adminDTO.getId());
        if (existingAdmin == null) {
            throw new BusinessException("管理员不存在");
        }

        // 如果修改了用户名，检查新用户名是否已存在
        if (StringUtils.hasText(adminDTO.getUsername()) &&
            !adminDTO.getUsername().equals(existingAdmin.getUsername())) {
            if (isUsernameExist(adminDTO.getUsername())) {
                throw new BusinessException("用户名已存在");
            }
        }

        Admin admin = adminDTO.toEntity();

        // 如果更新了密码，需要进行MD5加密
        if (StringUtils.hasText(admin.getPassword())) {
            String encryptedPassword = MD5Util.getMd5(admin.getPassword());
            if (encryptedPassword == null) {
                throw new BusinessException("密码加密失败");
            }
            admin.setPassword(encryptedPassword);
        }

        int result = adminMapper.updateById(admin);
        if (result <= 0) {
            throw new BusinessException("更新管理员失败");
        }
        return adminMapper.selectById(admin.getId()).toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("管理员ID不能为空");
        }

        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException("管理员不存在");
        }

        int result = adminMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<AdminDTO> getAll() throws BusinessException {
        List<Admin> admins = adminMapper.selectList(new LambdaQueryWrapper<Admin>()
                .orderByDesc(Admin::getId));
        return admins.stream().map(Admin::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean isExist(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("管理员ID不能为空");
        }
        return adminMapper.exists(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getId, id));
    }

    @Override
    public Boolean isUsernameExist(String username) throws BusinessException {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return adminMapper.exists(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username));
    }

    @Override
    public AdminDTO validateLogin(String username, String password) throws BusinessException {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            throw new BusinessException("用户名和密码不能为空");
        }

        // 对输入的密码进行MD5加密
        String encryptedPassword = MD5Util.getMd5(password);
        if (encryptedPassword == null) {
            throw new BusinessException("密码加密失败");
        }

        Admin admin = adminMapper.selectOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username)
                .eq(Admin::getPassword, encryptedPassword));

        if (admin == null) {
            throw new BusinessException("用户名或密码错误");
        }

        return admin.toDTO();
    }
}