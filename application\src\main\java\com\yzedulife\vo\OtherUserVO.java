package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 社会人士用户VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "社会人士用户VO")
public class OtherUserVO {

    @Schema(description = "社会人士ID")
    private Long id;

    @Schema(description = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(description = "姓名")
    private String name;
}
