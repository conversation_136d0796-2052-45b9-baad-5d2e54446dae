package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学生导入响应类
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生导入响应")
public class StudentImportResponse {

    @Schema(description = "总记录数")
    private Integer totalCount;

    @Schema(description = "成功导入数")
    private Integer successCount;

    @Schema(description = "失败记录数")
    private Integer failureCount;

    @Schema(description = "重复记录数")
    private Integer duplicateCount;

    @Schema(description = "失败记录详情")
    private List<ImportFailureDetail> failureDetails;

    @Schema(description = "重复记录详情")
    private List<ImportDuplicateDetail> duplicateDetails;

    /**
     * 导入失败详情
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Schema(description = "导入失败详情")
    public static class ImportFailureDetail {
        
        @Schema(description = "行号")
        private Integer rowNumber;
        
        @Schema(description = "学校名称")
        private String schoolName;
        
        @Schema(description = "班级名称")
        private String className;
        
        @Schema(description = "学号")
        private String studentNumber;
        
        @Schema(description = "姓名")
        private String name;
        
        @Schema(description = "失败原因")
        private String reason;
    }

    /**
     * 导入重复详情
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Schema(description = "导入重复详情")
    public static class ImportDuplicateDetail {

        @Schema(description = "行号")
        private Integer rowNumber;

        @Schema(description = "学校名称")
        private String schoolName;

        @Schema(description = "班级名称")
        private String className;

        @Schema(description = "学号")
        private String studentNumber;

        @Schema(description = "姓名")
        private String name;

        @Schema(description = "重复原因")
        private String reason;
    }
}
