package com.yzedulife.config;

import org.springframework.core.io.Resource;
import org.springframework.web.servlet.resource.PathResourceResolver;
import org.springframework.lang.NonNull;

import java.io.IOException;

public class SpaPathResourceResolver extends PathResourceResolver {

    @Override
    protected Resource getResource(@NonNull String resourcePath, @NonNull Resource location) throws IOException {
        Resource requestedResource = super.getResource(resourcePath, location);
        // 如果找不到资源，并且请求的不是API或静态资源，才返回 index.html
        if (requestedResource == null || !requestedResource.exists() || !requestedResource.isReadable()) {
            // [新增的判断逻辑]
            // 如果请求路径包含'.' (如 .js, .css, .png)，或者以/api/开头，则不进行转发
            // 这是一个简单的启发式规则，可以根据你的项目进行调整
            if (resourcePath.contains(".") || resourcePath.startsWith("/api/")) {
                // 对于这类明确指向文件或API的请求，如果找不到，就应该返回 null (最终导致404)
                return null;
            }
            // [逻辑修改]
            // 注意这里，我们不能再用 ClassPathResource 了，因为它从 jar 包内部找
            // 我们需要从外部文件系统中加载 index.html
            // location 代表的是 "file:./static/"
            // 所以我们直接从 location 获取 index.html
            return super.getResource("index.html", location);
        }
        return requestedResource;
    }
}
