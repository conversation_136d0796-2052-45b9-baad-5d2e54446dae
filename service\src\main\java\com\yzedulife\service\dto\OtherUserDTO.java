package com.yzedulife.service.dto;

import com.yzedulife.service.convert.OtherUserConvert;
import com.yzedulife.service.entity.OtherUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 社会人士用户DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OtherUserDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 社会人士ID
     */
    private Long id;

    /**
     * 手机号 (唯一)
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;

    public OtherUser toEntity() {
        return OtherUserConvert.INSTANCE.dto2entity(this);
    }
}
