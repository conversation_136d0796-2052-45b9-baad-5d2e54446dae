package com.yzedulife.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * SpringDoc API 文档相关配置
 */
@Configuration
public class SwaggerConfiguration {

    @Bean
    public OpenAPI springOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("意中问卷系统API文档")
                        .description("意中问卷系统API接口文档")
                        .version("1.0.0")
//                        .license(new License().name("Apache 2.0").url("https://www.apache.org/licenses/LICENSE-2.0")))
//                .externalDocs(new ExternalDocumentation()
//                        .description("替代 Springfox 的 SpringDOC 入门 文档")
//                        .url("https://www.cnblogs.com/jddreams/p/15922674.html")
                        );
    }

}