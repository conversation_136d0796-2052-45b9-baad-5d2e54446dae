package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 问卷请求VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "问卷请求VO")
public class QuestionnaireVO {
    
    @Schema(description = "问卷ID（更新时需要）")
    private Long id;
    
    @Schema(description = "问卷标题", required = true)
    @NotBlank(message = "问卷标题不能为空")
    private String title;

    @Schema(description = "描述（图片路径）")
    private String description;

    @Schema(description = "目标受众", example = "STUDENT 或 OTHER", required = true)
    @NotBlank(message = "目标受众不能为空")
    private String targetAudience;

    @Schema(description = "问卷状态", example = "0-草稿, 1-发布, 2-结束")
    private Integer status;

    @Schema(description = "考试时间（秒）", example = "3600")
    private Integer duration;
}
